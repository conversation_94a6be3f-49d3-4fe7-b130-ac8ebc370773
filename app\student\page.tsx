"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  BookOpen,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Play,
  Star,
  Award,
  Users,
  ChevronRight,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  RotateCcw
} from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { StudentErrorHandler } from "@/lib/student-error-handler"

interface StudentStats {
  totalQuizzes: number
  completedQuizzes: number
  averageScore: number
  totalPoints: number
  currentStreak: number
  rank: number
  totalStudents: number
  hoursSpent: number
}

interface RecentQuiz {
  id: string
  title: string
  type: string
  score: number
  maxScore: number
  completedAt: string
  difficulty: string
  timeSpent: number
}

interface UpcomingQuiz {
  id: string
  title: string
  type: string
  difficulty: string
  startTime: string
  duration: number
  questionsCount: number
  isEnrolled: boolean
}

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export default function StudentDashboard() {
  const [stats, setStats] = useState<StudentStats>({
    totalQuizzes: 0,
    completedQuizzes: 0,
    averageScore: 0,
    totalPoints: 0,
    currentStreak: 0,
    rank: 0,
    totalStudents: 0,
    hoursSpent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [recentQuizzes, setRecentQuizzes] = useState<RecentQuiz[]>([])

  const [upcomingQuizzes, setUpcomingQuizzes] = useState<UpcomingQuiz[]>([])

  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await StudentErrorHandler.fetchWithErrorHandling(
        '/api/student/dashboard',
        {},
        'Fetching dashboard data'
      )

      if (data.success) {
        setStats(data.data.stats)
        setRecentQuizzes(data.data.recentAttempts || [])
        setUpcomingQuizzes(data.data.enrolledQuizzes || [])
        setRecentAchievements(data.data.achievements || [])
      } else {
        throw new Error(data.message || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      const errorMessage = StudentErrorHandler.handleError(
        error as Error,
        'Fetching dashboard data',
        {
          showToast: true,
          retryCallback: () => fetchDashboardData(),
          fallbackMessage: 'Failed to load dashboard data'
        }
      )
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50'
      case 'rare':
        return 'border-blue-300 bg-blue-50'
      case 'epic':
        return 'border-purple-300 bg-purple-50'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <Card className="max-w-md">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <XCircle className="h-16 w-16 text-destructive mx-auto" />
                  <h3 className="text-xl font-semibold">Failed to Load Dashboard</h3>
                  <p className="text-muted-foreground">
                    {error}
                  </p>
                  <Button onClick={fetchDashboardData} variant="default">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="p-6 space-y-8">
        {/* Welcome Header */}
        <div className="text-center space-y-4">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Welcome back, Student! 👋
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Ready to continue your learning journey?
            </p>
          </motion.div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.completedQuizzes}</div>
                    <p className="text-blue-100">Quizzes Completed</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-200" />
                </div>
                <div className="mt-4">
                  <div className="relative h-2 w-full overflow-hidden rounded-full bg-blue-400/30">
                    <div
                      className="h-full bg-white transition-all duration-300 ease-in-out rounded-full"
                      style={{ width: `${Math.min((stats.completedQuizzes / Math.max(stats.totalQuizzes, 1)) * 100, 100)}%` }}
                    />
                  </div>
                  <p className="text-xs text-blue-100 mt-1">
                    {stats.completedQuizzes} of {stats.totalQuizzes} total
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

        {/* Dashboard Widgets Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Quick Actions Widget */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={isVisible ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="lg:col-span-4"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link href="/student/browse">
                  <AnimatedButton
                    variant="primary"
                    size="lg"
                    className="w-full justify-start group"
                  >
                    <Play className="w-5 h-5 mr-3 group-hover:scale-110 transition-transform" />
                    Start New Quiz
                  </AnimatedButton>
                </Link>

                <Link href="/student/history">
                  <AnimatedButton
                    variant="outline"
                    size="md"
                    className="w-full justify-start group"
                  >
                    <BarChart3 className="w-4 h-4 mr-3 group-hover:scale-110 transition-transform" />
                    View Analytics
                  </AnimatedButton>
                </Link>

                <Link href="/student/profile">
                  <AnimatedButton
                    variant="outline"
                    size="md"
                    className="w-full justify-start group"
                  >
                    <Users className="w-4 h-4 mr-3 group-hover:scale-110 transition-transform" />
                    Update Profile
                  </AnimatedButton>
                </Link>
              </div>
            </div>
          </motion.div>

          {/* Recent Activity Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="lg:col-span-8"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Activity className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  Recent Activity
                </h3>
                <Link href="/student/history" className="group text-violet-600 dark:text-violet-400">
                  <AnimatedButton variant="ghost" size="sm">
                    View All
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </AnimatedButton>
                </Link>
              </div>

              <div className="space-y-3 max-h-80 overflow-y-auto">
                {recentQuizzes.map((quiz, index) => (
                  <motion.div
                    key={quiz.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    className="flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-black/20 border border-white/30 dark:border-white/10 hover:bg-white/70 dark:hover:bg-black/30 transition-all duration-300 group"
                  >
                    <div className="flex items-center gap-4 flex-1">
                      <div className={`w-3 h-3 rounded-full ${
                        quiz.score / quiz.maxScore >= 0.9 ? 'bg-green-500' :
                        quiz.score / quiz.maxScore >= 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />

                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                          {quiz.title}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(quiz.completedAt).toLocaleDateString()}
                          </span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {quiz.timeSpent} min
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className={`text-lg font-bold ${getScoreColor(quiz.score, quiz.maxScore)}`}>
                        {Math.round((quiz.score / quiz.maxScore) * 100)}%
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Upcoming Quizzes Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="lg:col-span-6"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                  Upcoming Quizzes
                </h3>
                <Link href="/student/browse" className="group text-violet-600 dark:text-violet-400">
                  <AnimatedButton variant="ghost" size="sm">
                    Browse All
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </AnimatedButton>
                </Link>
              </div>

              <div className="space-y-3">
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No upcoming scheduled quizzes</p>
                  <p className="text-sm mt-1">Browse available quizzes to get started</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Study Streak Widget */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="lg:col-span-6"
          >
            <div className="glass rounded-2xl p-6 h-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Zap className="w-5 h-5 text-orange-500" />
                  Study Streak
                </h3>
              </div>

              <div className="text-center">
                <div className="text-4xl font-bold text-gradient bg-gradient-accent mb-2">
                  {stats.currentStreak}
                </div>
                <div className="text-gray-600 dark:text-gray-400 mb-4">Days in a row</div>

                <div className="flex justify-center gap-1 mb-4">
                  {[...Array(7)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium ${
                        i < stats.currentStreak
                          ? 'bg-gradient-accent text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-400'
                      }`}
                    >
                      {i + 1}
                    </div>
                  ))}
                </div>

                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Keep going! You're doing great 🔥
                </p>
              </div>
            </div>
          </motion.div>
      </div>
    </div>
    </div>
  )
  }

