"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  BookOpen,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Play,
  Star,
  Award,
  Users,
  ChevronRight,
  Zap,
  Brain,
  CheckCircle,
  XCircle,
  RotateCcw
} from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { StudentErrorHandler } from "@/lib/student-error-handler"

interface StudentStats {
  totalQuizzes: number
  completedQuizzes: number
  averageScore: number
  totalPoints: number
  currentStreak: number
  rank: number
  totalStudents: number
  hoursSpent: number
}

interface RecentQuiz {
  id: string
  title: string
  type: string
  score: number
  maxScore: number
  completedAt: string
  difficulty: string
  timeSpent: number
}

interface UpcomingQuiz {
  id: string
  title: string
  type: string
  difficulty: string
  startTime: string
  duration: number
  questionsCount: number
  isEnrolled: boolean
}

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export default function StudentDashboard() {
  const [stats, setStats] = useState<StudentStats>({
    totalQuizzes: 0,
    completedQuizzes: 0,
    averageScore: 0,
    totalPoints: 0,
    currentStreak: 0,
    rank: 0,
    totalStudents: 0,
    hoursSpent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [recentQuizzes, setRecentQuizzes] = useState<RecentQuiz[]>([])

  const [upcomingQuizzes, setUpcomingQuizzes] = useState<UpcomingQuiz[]>([])

  const [recentAchievements, setRecentAchievements] = useState<Achievement[]>([])

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    setLoading(true)
    setError(null)

    try {
      const data = await StudentErrorHandler.fetchWithErrorHandling(
        '/api/student/dashboard',
        {},
        'Fetching dashboard data'
      )

      if (data.success) {
        setStats(data.data.stats)
        setRecentQuizzes(data.data.recentAttempts || [])
        setUpcomingQuizzes(data.data.enrolledQuizzes || [])
        setRecentAchievements(data.data.achievements || [])
      } else {
        throw new Error(data.message || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      const errorMessage = StudentErrorHandler.handleError(
        error as Error,
        'Fetching dashboard data',
        {
          showToast: true,
          retryCallback: () => fetchDashboardData(),
          fallbackMessage: 'Failed to load dashboard data'
        }
      )
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-300 bg-gray-50'
      case 'rare':
        return 'border-blue-300 bg-blue-50'
      case 'epic':
        return 'border-purple-300 bg-purple-50'
      case 'legendary':
        return 'border-yellow-300 bg-yellow-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <Card className="max-w-md">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <XCircle className="h-16 w-16 text-destructive mx-auto" />
                  <h3 className="text-xl font-semibold">Failed to Load Dashboard</h3>
                  <p className="text-muted-foreground">
                    {error}
                  </p>
                  <Button onClick={fetchDashboardData} variant="default">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="p-6 space-y-8">
        {/* Welcome Header */}
        <div className="text-center space-y-4">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Welcome back, Student! 👋
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Ready to continue your learning journey?
            </p>
          </motion.div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.completedQuizzes}</div>
                    <p className="text-blue-100">Quizzes Completed</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-200" />
                </div>
                <div className="mt-4">
                  <div className="relative h-2 w-full overflow-hidden rounded-full bg-blue-400/30">
                    <div
                      className="h-full bg-white transition-all duration-300 ease-in-out rounded-full"
                      style={{ width: `${Math.min((stats.completedQuizzes / Math.max(stats.totalQuizzes, 1)) * 100, 100)}%` }}
                    />
                  </div>
                  <p className="text-xs text-blue-100 mt-1">
                    {stats.completedQuizzes} of {stats.totalQuizzes} total
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Average Score Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-green-500 to-green-600 text-white border-0">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.averageScore}%</div>
                    <p className="text-green-100">Average Score</p>
                  </div>
                  <Target className="h-8 w-8 text-green-200" />
                </div>
                <div className="mt-4">
                  <div className="relative h-2 w-full overflow-hidden rounded-full bg-green-400/30">
                    <div
                      className="h-full bg-white transition-all duration-300 ease-in-out rounded-full"
                      style={{ width: `${Math.min(stats.averageScore, 100)}%` }}
                    />
                  </div>
                  <p className="text-xs text-green-100 mt-1">
                    {stats.averageScore >= 90 ? 'Excellent!' : stats.averageScore >= 70 ? 'Good work!' : 'Keep improving!'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Current Streak Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">{stats.currentStreak}</div>
                    <p className="text-orange-100">Day Streak</p>
                  </div>
                  <Zap className="h-8 w-8 text-orange-200" />
                </div>
                <div className="mt-4">
                  <div className="flex items-center space-x-1">
                    {[...Array(Math.min(stats.currentStreak, 7))].map((_, i) => (
                      <div key={i} className="h-2 w-2 bg-white rounded-full" />
                    ))}
                    {stats.currentStreak > 7 && (
                      <span className="text-xs text-orange-100 ml-2">+{stats.currentStreak - 7}</span>
                    )}
                  </div>
                  <p className="text-xs text-orange-100 mt-1">
                    {stats.currentStreak > 0 ? 'Keep it up!' : 'Start your streak today!'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Rank Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="hover:shadow-lg transition-shadow bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold">#{stats.rank}</div>
                    <p className="text-purple-100">Your Rank</p>
                  </div>
                  <Trophy className="h-8 w-8 text-purple-200" />
                </div>
                <div className="mt-4">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-purple-200" />
                    <span className="text-xs text-purple-100">
                      out of {stats.totalStudents} students
                    </span>
                  </div>
                  <p className="text-xs text-purple-100 mt-1">
                    {stats.rank <= 10 ? 'Top performer!' : stats.rank <= 50 ? 'Great job!' : 'Keep climbing!'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Recent Activity & Upcoming Quizzes */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Quizzes */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>Recent Activity</span>
                </CardTitle>
                <CardDescription>Your latest quiz attempts</CardDescription>
              </CardHeader>
              <CardContent>
                {recentQuizzes.length > 0 ? (
                  <div className="space-y-4">
                    {recentQuizzes.slice(0, 3).map((quiz) => (
                      <div key={quiz.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <div className="flex-1">
                          <h4 className="font-medium">{quiz.title}</h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className={getDifficultyColor(quiz.difficulty)}>
                              {quiz.difficulty}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(quiz.completedAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-lg font-bold ${getScoreColor(quiz.score, quiz.maxScore)}`}>
                            {quiz.score}/{quiz.maxScore}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {Math.round((quiz.score / quiz.maxScore) * 100)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No recent activity</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Take your first quiz to see your progress here
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Upcoming Quizzes */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Upcoming Quizzes</span>
                </CardTitle>
                <CardDescription>Quizzes you're enrolled in</CardDescription>
              </CardHeader>
              <CardContent>
                {upcomingQuizzes.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingQuizzes.slice(0, 3).map((quiz) => (
                      <div key={quiz.id} className="p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{quiz.title}</h4>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className={getDifficultyColor(quiz.difficulty)}>
                                {quiz.difficulty}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {quiz.questionsCount} questions
                              </span>
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {new Date(quiz.startTime).toLocaleString()}
                            </div>
                          </div>
                          <Link href={`/quiz/${quiz.id}`}>
                            <Button size="sm" variant="outline">
                              <Play className="h-4 w-4 mr-1" />
                              Start
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No upcoming quizzes</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Check back later for new quiz opportunities
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Recent Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-5 w-5" />
                <span>Recent Achievements</span>
              </CardTitle>
              <CardDescription>Your latest accomplishments</CardDescription>
            </CardHeader>
            <CardContent>
              {recentAchievements.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recentAchievements.slice(0, 6).map((achievement) => (
                    <div
                      key={achievement.id}
                      className={`p-4 rounded-lg border-2 ${getRarityColor(achievement.rarity)} transition-all hover:scale-105`}
                    >
                      <div className="text-center">
                        <div className="text-2xl mb-2">{achievement.icon}</div>
                        <h4 className="font-semibold text-sm">{achievement.title}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {achievement.description}
                        </p>
                        <div className="flex items-center justify-center mt-2">
                          <Star className="h-3 w-3 text-yellow-500 mr-1" />
                          <span className="text-xs capitalize text-muted-foreground">
                            {achievement.rarity}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Award className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No achievements yet</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Complete quizzes to unlock achievements
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>Quick Actions</span>
              </CardTitle>
              <CardDescription>Jump into learning</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link href="/quiz/browse">
                  <Button className="w-full h-20 flex flex-col items-center justify-center space-y-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
                    <BookOpen className="h-6 w-6" />
                    <span>Browse Quizzes</span>
                  </Button>
                </Link>
                <Link href="/student/progress">
                  <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                    <TrendingUp className="h-6 w-6" />
                    <span>View Progress</span>
                  </Button>
                </Link>
                <Link href="/student/achievements">
                  <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center space-y-2">
                    <Trophy className="h-6 w-6" />
                    <span>Achievements</span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>

      </div>
    </div>
  )
}